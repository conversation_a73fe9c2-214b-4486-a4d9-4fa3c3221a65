import {
  fetchAppointmentByCategory,
  fetchAppointments,
  fetchCategoriesAppointments,
} from './appointment';
import { fetchClassesByOrgId } from './classes';
import { fetchClientInfoByOrgId } from './client-info';
import { fetchEventsByDate } from './events';
import { toggleFavorite } from './favorites';
import { fetchPrograms } from './programs';
import { fetchTrainersByOrgId, fetchTrainersBySessionId } from './trainer';
import { fetchUserInfo } from './user';
import {
  fetchActivitiesByDate,
  reserveActivity,
  cancelActivityReservation,
  fetchAvailableDurations,
} from './activities';
import {
  fetchChallenges,
  joinChallenge,
  fetchChallengeById,
  leaveChallenge,
} from './challenges';
import { fetchSupportTypes, submitSupportEmail } from './support';
import { fetchFacilities } from './facilities';
import { fetchChallengeEventLogs } from './challenge-logs';
import {
  fetchOnDemandCategories,
  fetchOnDemandVideosByCategory,
} from './on-demand';

export const apiClient = {
  getTrainersBySessionId: fetchTrainersBySessionId,
  getTrainersByOrgId: fetchTrainersByOrgId,
  getAppointmentsByCategory: fetchAppointmentByCategory,
  getAppointments: fetchAppointments,
  getCategoriesAppointments: fetchCategoriesAppointments,
  getClassesByOrgId: fetchClassesByOrgId,
  getEventsByDate: fetchEventsByDate,
  getPrograms: fetchPrograms,
  getUserInfo: fetchUserInfo,
  toggleFavorite,
  getClientInfoByOrgId: fetchClientInfoByOrgId,
  getActivitiesByDate: fetchActivitiesByDate,
  reserveActivity,
  cancelActivityReservation,
  getAvailableDurations: fetchAvailableDurations,
  getChallenges: fetchChallenges,
  joinChallenge,
  getChallengeById: fetchChallengeById,
  leaveChallenge,
  getSupportTypes: fetchSupportTypes,
  submitSupportEmail,
  getFacilities: fetchFacilities,
  getChallengeEventLogs: fetchChallengeEventLogs,
  getOnDemandCategories: fetchOnDemandCategories,
  getOnDemandVideosByCategory: fetchOnDemandVideosByCategory,
};
