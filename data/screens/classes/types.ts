export interface ClassDetailsResponse {
  id: number;
  gym_id: number;
  room_id: number;
  start_time: string;
  end_time: string;
  name: string;
  description: string;
  allow_reservation_date: string;
  advance_time: number;
  spots: number;
  walkin_spots: number;
  is_sgt: number;
  class_category_id: number;
  tags: string[];
  days_of_week: string[];
  allow_waitlist: number;
  waitlist_spots: number;
  gym_name: string;
  room_name: string;
  slot_id: number;
  category: string;
  instructor_id: number;
  instructor_first_name: string;
  instructor_last_name: string;
  facility_closed: boolean;
  cancelled: boolean;
  allow_reservations: boolean;
  instructor: string;
  is_class_subbed: boolean;
  reservation_count: number;
  waitlist_count: number;
  spots_available: number;
  waitlist_spots_available: number;
  occupancy_total: number;
  images: string[];
  facility_closed_reason?: string;
  cancel_reason?: string;
  subbing_instructor?: string;
  is_recommended?: boolean;
  is_new?: boolean;
  is_full?: boolean;
  is_started?: boolean;
  is_waitlist_available?: boolean;
  start_day?: string;
  date_time?: string;
  current_user_reservation?: Record<string, string | number>;
  is_virtual?: boolean;
  current_user_waitlist?: Record<string, string | number>;
  position_index?: number;
  class_id?: number;
  class_name?: string;
  equipment_name?: string;
  date_info?: ClassDateInfo[];
  equipment_id?: number;
  first_name?: string;
  last_name?: string;
  attending_persons?: number;
  start?: string;
  end?: string;
  title?: string;
  selected_date?: string;
  day_record?: ClassDateInfo;
  date?: string;
  dow?: string;
  walkin_spots_available?: boolean;
  is_past?: boolean;
  virtual_spots_available?: number;
  class_type: 'Virtual' | 'Live' | 'Virtual & Live';
  reservation_id?: number;
  instructor_image?: string;
  is_favourite?: boolean;
  subbing_instructor_id?: number;
}

export interface ClassDateInfo {
  cancel_reason?: string;
  cancelled: boolean;
  date: string;
  is_class_subbed: boolean;
  subbing_instructor?: string;
  dow: string;
  id?: number;
  spots_available?: number;
  current_user_reservation?: Record<string, string | number>;
  is_past?: boolean;
  is_started?: boolean;
}
