import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { VStack } from '@/components/ui/vstack';
import { HStack } from '@/components/ui/hstack';
import { Text } from '@/components/ui/text';
import { Button, ButtonText } from '@/components/ui/button';
import { Icon } from '@/components/ui/icon';
import { Pressable } from '@/components/ui/pressable';
import { ScrollView } from '@/components/ui/scroll-view';
import {
  Actionsheet,
  ActionsheetBackdrop,
  ActionsheetContent,
  ActionsheetDragIndicator,
  ActionsheetDragIndicatorWrapper,
} from '@/components/ui/actionsheet';

import { Setting4 } from 'iconsax-react-nativejs';
import { AvailableTimeSlot } from '@/data/screens/activities/types';

import { Badge, BadgeText } from '../ui/badge';

// Filter field types
export interface SelectOption {
  label: string;
  value: string;
}

export interface MultiSelectField {
  type: 'multiselect';
  key: string;
  label: string;
  options: SelectOption[];
  placeholder?: string;
  // Optional: controls how options are listed in the sheet
  listStyle?: 'default' | 'pill';
}

export interface SingleSelectField {
  type: 'select';
  key: string;
  label: string;
  options: SelectOption[];
  placeholder?: string;
  // Optional: controls how options are listed in the sheet
  listStyle?: 'default' | 'pill';
}

export interface TimeField {
  type: 'time';
  key: string;
  label: string;
  timeSlots: AvailableTimeSlot[];
  placeholder?: string;
}

export interface TimeRangeField {
  type: 'timeRange';
  key: string;
  label: string;

  startTime?: string;
  endTime?: string;

  dayDate?: Date;

  minTime?: number;
  maxTime?: number;
  step?: number;
}

export interface DateField {
  type: 'date';
  key: string;
  label: string;
  placeholder?: string;
}

export interface ToggleField {
  type: 'toggle';
  key: string;
  label: string;
  description?: string;
}

export type FilterField =
  | MultiSelectField
  | SingleSelectField
  | TimeField
  | TimeRangeField
  | DateField
  | ToggleField;

export interface FilterValues {
  // Allow timeRange values to use the new consistent format
  [key: string]:
    | string
    | string[]
    | boolean
    | { startTime: string; endTime: string } // Updated to match new TimeRangeValue format
    | Date
    | null;
}

export interface FilterComponentProps {
  fields: FilterField[];
  values: FilterValues;
  onValuesChange: (values: FilterValues) => void;
  onApply: (values: FilterValues) => void;
  onReset: () => void;
  title?: string;
  activeFilterCount?: number;
  size?: number;
}

import MultiSelectFieldComponent from './filter/multi-select-field';
import SingleSelectFieldComponent from './filter/single-select-field';
import TimeFieldComponent from './filter/time-field';
import DateFieldComponent from './filter/date-field';
import ToggleFieldComponent from './filter/toggle-field';
import { TimeRangeFieldComponent } from './filter/time-ranger';

type FieldType = FilterField['type'];

type FieldByType<K extends FieldType> = Extract<FilterField, { type: K }>;

type FieldValueByType = {
  multiselect: string[];
  select: string;
  time: string;
  timeRange: { startTime: string; endTime: string };
  date: Date | null;
  toggle: boolean;
};

type RegistryItem<K extends FieldType> = {
  Component: React.ComponentType<{
    field: FieldByType<K>;
    value: FieldValueByType[K];
    onChange: (v: FieldValueByType[K]) => void;
  }>;
  getDefaultValue: (field: FieldByType<K>) => FieldValueByType[K];
  normalizeValue: (
    value: unknown,
    field: FieldByType<K>
  ) => FieldValueByType[K];
};

const FIELD_REGISTRY: { [K in FieldType]: RegistryItem<K> } = {
  multiselect: {
    Component: MultiSelectFieldComponent as React.ComponentType<{
      field: FieldByType<'multiselect'>;
      value: FieldValueByType['multiselect'];
      onChange: (v: FieldValueByType['multiselect']) => void;
    }>,
    getDefaultValue: () => [],
    normalizeValue: v => (Array.isArray(v) ? v : []),
  },
  select: {
    Component: SingleSelectFieldComponent as React.ComponentType<{
      field: FieldByType<'select'>;
      value: FieldValueByType['select'];
      onChange: (v: FieldValueByType['select']) => void;
    }>,
    getDefaultValue: () => '',
    normalizeValue: v => (typeof v === 'string' ? v : ''),
  },
  time: {
    Component: TimeFieldComponent as React.ComponentType<{
      field: FieldByType<'time'>;
      value: FieldValueByType['time'];
      onChange: (v: FieldValueByType['time']) => void;
    }>,
    getDefaultValue: () => '',
    normalizeValue: v => (typeof v === 'string' ? v : ''),
  },
  timeRange: {
    Component: TimeRangeFieldComponent as React.ComponentType<{
      field: FieldByType<'timeRange'>;
      value: FieldValueByType['timeRange'];
      onChange: (v: FieldValueByType['timeRange']) => void;
    }>,
    getDefaultValue: () => ({ startTime: '04:00', endTime: '24:00' }),
    normalizeValue: v => {
      if (
        v &&
        typeof v === 'object' &&
        'startTime' in v &&
        'endTime' in v &&
        typeof (v as { startTime?: unknown }).startTime === 'string' &&
        typeof (v as { endTime?: unknown }).endTime === 'string'
      ) {
        return v as { startTime: string; endTime: string };
      }
      return { startTime: '04:00', endTime: '24:00' };
    },
  },
  date: {
    Component: DateFieldComponent as React.ComponentType<{
      field: FieldByType<'date'>;
      value: FieldValueByType['date'];
      onChange: (v: FieldValueByType['date']) => void;
    }>,
    getDefaultValue: () => null,
    normalizeValue: v => (v instanceof Date || v === null ? v : null),
  },
  toggle: {
    Component: ToggleFieldComponent as React.ComponentType<{
      field: FieldByType<'toggle'>;
      value: FieldValueByType['toggle'];
      onChange: (v: FieldValueByType['toggle']) => void;
    }>,
    getDefaultValue: () => false,
    normalizeValue: v => Boolean(v),
  },
};

export const FilterComponent = ({
  fields,
  values,
  onValuesChange,
  onApply,
  onReset,
  title = 'Filter',
  activeFilterCount = 0,
  size = 80,
}: FilterComponentProps) => {
  const [localValues, setLocalValues] = useState<FilterValues>(values);
  const [isOpen, setIsOpen] = useState(false);

  useEffect(() => {
    if (isOpen) {
      setLocalValues(values);
    }
  }, [values, isOpen]);

  const handleFieldChange = useCallback(
    (
      key: string,
      value:
        | string
        | string[]
        | boolean
        | { startTime: string; endTime: string }
        | Date
        | null
    ) => {
      const newValues = { ...localValues, [key]: value };
      setLocalValues(newValues);
      onValuesChange(newValues);
    },
    [localValues, onValuesChange]
  );

  const handleApply = useCallback(() => {
    onApply?.(localValues);
    setIsOpen(false);
  }, [localValues, onApply]);

  const handleReset = useCallback(() => {
    const resetValues: FilterValues = {};
    fields.forEach(field => {
      // Narrow field for registry access by discriminated union
      const value = (
        FIELD_REGISTRY as Record<string, RegistryItem<FilterField['type']>>
      )[field.type].getDefaultValue(field as never);
      resetValues[field.key] = value as FilterValues[string];
    });
    setLocalValues(resetValues);
    onReset();
    setIsOpen(false);
  }, [fields, onReset]);

  const onChangeHandlers = useMemo(() => {
    const handlers: Record<string, (value: unknown) => void> = {};
    fields.forEach(field => {
      handlers[field.key] = (value: unknown) =>
        handleFieldChange(field.key, value as FilterValues[string]);
    });
    return handlers;
  }, [fields, handleFieldChange]);

  const renderField = useCallback(
    (field: FilterField) => {
      const registry = FIELD_REGISTRY[field.type] as RegistryItem<
        (typeof field)['type']
      >;

      const normalizedValue = registry.normalizeValue(
        localValues[field.key],
        field as never
      ) as FieldValueByType[(typeof field)['type']];

      const onChange = onChangeHandlers[field.key] as (
        v: FieldValueByType[(typeof field)['type']]
      ) => void;

      const Component = registry.Component as React.ComponentType<{
        field: FieldByType<(typeof field)['type']>;
        value: FieldValueByType[(typeof field)['type']];
        onChange: (v: FieldValueByType[(typeof field)['type']]) => void;
      }>;

      return (
        <Component
          key={field.key}
          field={field as never}
          value={normalizedValue}
          onChange={onChange}
        />
      );
    },
    [localValues, onChangeHandlers]
  );

  return (
    <>
      <Pressable
        onPress={() => setIsOpen(true)}
        className={`w-10 h-10 rounded-full items-center justify-center border relative ${
          activeFilterCount > 0
            ? 'bg-[#00BFE0] border-[#00BFE0]'
            : 'bg-background-50 border-outline-200'
        }`}
      >
        <Icon
          color={activeFilterCount > 0 ? 'white' : 'gray'}
          as={() => (
            <Setting4
              size="20"
              color={activeFilterCount > 0 ? 'white' : 'gray'}
            />
          )}
          className={
            activeFilterCount > 0 ? 'text-white' : 'text-typography-600'
          }
          size="md"
        />

        {/* Filter count badge */}
        {activeFilterCount > 0 && (
          <Badge
            className="absolute -top-1 -right-1 bg-red-500 min-w-5 h-5 rounded-full items-center justify-center px-1"
            variant="solid"
          >
            <BadgeText className="text-white text-xs font-dm-sans-bold">
              {activeFilterCount > 99 ? '99+' : activeFilterCount.toString()}
            </BadgeText>
          </Badge>
        )}
      </Pressable>
      {isOpen && (
        <Actionsheet isOpen={isOpen} onClose={() => setIsOpen(false)}>
          <ActionsheetBackdrop className="bg-black opacity-50" />
          <ActionsheetContent
            style={{
              maxHeight: `${size}%`,
            }}
            className={`bg-white flex-1`}
          >
            <ActionsheetDragIndicatorWrapper>
              <ActionsheetDragIndicator />
            </ActionsheetDragIndicatorWrapper>

            {/* Header */}
            <HStack className="items-center  mb-4">
              <Text className="text-xl font-dm-sans-bold text-typography-900 text-left">
                {title}
              </Text>
            </HStack>

            {/* Filter Fields */}
            <ScrollView
              className="flex-1 w-full"
              showsVerticalScrollIndicator={false}
              contentContainerClassName="flex-grow"
            >
              <VStack space="lg" className=" w-full">
                {fields.map(renderField)}
              </VStack>
            </ScrollView>

            {/* Footer Actions */}
            <HStack
              space="md"
              className="px-6 py-6 border-t border-outline-100 bg-white"
            >
              <Button
                variant="outline"
                onPress={handleReset}
                className="flex-1 border-typography-300 bg-transparent rounded-full h-12"
              >
                <ButtonText className="text-typography-600 font-dm-sans-medium text-base">
                  Reset all
                </ButtonText>
              </Button>
              <Button
                onPress={handleApply}
                className="flex-1 bg-[#00BFE0] rounded-full h-12"
              >
                <ButtonText className="text-white font-dm-sans-medium text-base">
                  Apply
                </ButtonText>
              </Button>
            </HStack>
          </ActionsheetContent>
        </Actionsheet>
      )}
    </>
  );
};

FilterComponent.displayName = 'FilterComponent';
export default FilterComponent;
