import React from 'react';
import { ClassDetailsResponse } from '@/data/screens/classes/types';
import { obtainDateFrame } from '@/data/common/common.utils';
import { obtainSpotsAvailable } from '@/data/screens/classes/utils';
import { differenceInMinutes, format, parse, parseISO } from 'date-fns';
import { DATE_FORMAT } from '@/constants/date-formats';
import { ClassStatusButton } from '../class-card/class-button';
import { useFavoriteMutation } from '@/data/screens/common/queries/useFavoriteMutation';
import { ClassDetailsSkeleton } from './class-details-skeleton';
import {
  BaseDetailsView,
  MetricsSection,
  InfoSection,
  InstructorSection,
  DescriptionSection,
} from '@/components/shared/details-view';
import { AddToCalendarButton } from '@/components/shared/add-to-calendar';
import { HStack } from '@/components/ui/hstack';
import { View } from 'react-native';

interface ClassDetailsProps {
  classItem: ClassDetailsResponse;
  selectedDate?: string;
  isLoading?: boolean;
}

export const ClassDetails: React.FC<ClassDetailsProps> = ({
  classItem,
  selectedDate,
  isLoading = false,
}) => {
  const { mutate: favoriteMutation } = useFavoriteMutation();

  if (isLoading) {
    return <ClassDetailsSkeleton />;
  }

  const calculateDuration = (startTime: string, endTime: string) => {
    const start = parse(startTime, 'HH:mm:ss', new Date());
    const end = parse(endTime, 'HH:mm:ss', new Date());
    const minutes = differenceInMinutes(end, start);

    return `${minutes} MINS`;
  };

  // Get intensity color and display
  const getIntensityInfo = (category: string) => {
    const intensity = category?.toLowerCase() || 'low';
    switch (intensity) {
      case 'high':
        return {
          color: 'bg-error-500',
          label: 'High',
        };
      case 'medium':
        return {
          color: 'bg-warning-500',
          label: 'Medium',
        };
      case 'low':
      default:
        return {
          color: 'bg-success-500',
          label: 'Low',
        };
    }
  };

  const selectedClassDate = selectedDate ?? new Date().toISOString();
  const instructorName = `${classItem.instructor_first_name} ${classItem.instructor_last_name}`;

  const handleFavorite = () => {
    return favoriteMutation({
      type: 'class',
      item_id: classItem.id,
    });
  };


  return (
    <BaseDetailsView
      item={classItem}
      heroImageUrl={classItem.images?.[0]}
      onFavorite={handleFavorite}
      renderActionButton={() => {
        const hasReservation = classItem?.current_user_reservation;
        return (
          <HStack space="xs" className="w-full items-center">
            <View className={hasReservation ? "flex-[0.92]" : "flex-1"}>
              <ClassStatusButton
                size="lg"
                data={classItem}
                selectedDate={selectedDate}
              />
            </View>

            {hasReservation && (
              <View className="flex-[0.08] items-center justify-center pl-4">
                <AddToCalendarButton
                  name={classItem.name}
                  description={classItem.description}
                  startTime={classItem.start_time}
                  endTime={classItem.end_time}
                  location={[classItem.gym_name, classItem.room_name]
                    .filter(Boolean)
                    .join(', ')}
                  instructor={`${classItem.instructor_first_name} ${classItem.instructor_last_name}`}
                  reservationId={Number(classItem.reservation_id)}
                  selectedDate={selectedClassDate}
                />
              </View>
            )}
          </HStack>
        );
      }}
    >
      <MetricsSection
        intensity={getIntensityInfo(classItem.category)}
        duration={calculateDuration(classItem.start_time, classItem.end_time)}
        showIntensity={true}
      />

      <InfoSection
        date={format(
          parseISO(selectedClassDate),
          DATE_FORMAT.DAY_MONTH_DAY_YEAR
        )}
        timeFrame={obtainDateFrame(classItem.start_time, classItem.end_time)}
        spotsLeft={obtainSpotsAvailable(classItem.class_type, classItem) || 0}
        location={classItem.gym_name}
        room={classItem.room_name}
      />

      <InstructorSection
        instructorName={instructorName}
        instructorImage={classItem.instructor_image}
        instructorId={
          classItem.is_class_subbed
            ? Number(classItem.subbing_instructor_id)
            : classItem.instructor_id
        }
        showReadAbout={true}
        is_class_subbed={classItem?.is_class_subbed}
        subbing_instructor={classItem?.subbing_instructor}
      />

      {classItem.description && (
        <DescriptionSection description={classItem.description} />
      )}
    </BaseDetailsView>
  );
};

export default ClassDetails;
