import React, { use<PERSON>emo, useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, ButtonText } from '@/components/ui/button';
import { Text } from '@/components/ui/text';
import { Icon } from '@/components/ui/icon';
import {
  Actionsheet,
  ActionsheetBackdrop,
  ActionsheetContent,
  ActionsheetDragIndicator,
  ActionsheetDragIndicatorWrapper,
  ActionsheetItem,
  ActionsheetItemText,
} from '@/components/ui/actionsheet';
import { ClassDetailsResponse } from '@/data/screens/classes/types';
import {
  actionDisabled,
  getButtonClass,
  getButtonText,
  getClassAvailabilityAction,
} from './action-button-utils';

import { StatusActionSheet } from '@/components/shared/status-actionsheet';
import { reserveActions } from './reserve-utils';
import { ClassActionState, ClassType } from './types';
import { formatDate } from '@/data/common/common.utils';
import { noop } from 'lodash/fp';
import { useCancelReservation } from '@/data/screens/reservations/mutations/useCancelReservation';
import { useReserveMutation } from '@/data/screens/common/mutations/useReserveMutation';
import type { ReservationData } from '@/data/screens/common/mutations/useReserveMutation';
import { useExitWaitlistMutation } from '@/data/screens/reservations/mutations/useExitWaitlistMutation';
import { ChevronDown } from 'lucide-react-native';
import { useCalendarSynced } from '@/hooks/useCalendarSynced';
import { CalendarSuccessActionSheet } from '@/components/shared/calendar-success-actionsheet';

type ClassButtonProps = {
  data: ClassDetailsResponse;
  selectedDate?: Date | string;
  size?: 'sm' | 'md' | 'lg';
};

export const ClassStatusButton = ({
  data,
  selectedDate,
  size = 'sm',
}: ClassButtonProps) => {
  const availability = useMemo(() => getClassAvailabilityAction(data), [data]);

  const [showToast, setShowToast] = useState(false);
  const [showTypeSheet, setShowTypeSheet] = useState(false);
  const [showCalendarSheet, setShowCalendarSheet] = useState(false);

  const {
    mutate: reserveClass,
    isPending: isReserving,
    error,
  } = useReserveMutation({
    onSuccess: () => {
      setShowToast(true);
    },
  });

  const { mutate: cancelReservation, isPending: isCancelling } =
    useCancelReservation();

  const { mutate: exitWaitlist, isPending: isLeavingWaitlist } =
    useExitWaitlistMutation();

  const { isSynced: isCalendarSynced, setIsSynced: setCalendarSynced } =
    useCalendarSynced(
      Number.isFinite(data.current_user_reservation?.reservation_id)
        ? (data.current_user_reservation?.reservation_id as number)
        : undefined
    );

  if (!availability) return null;

  const { actionState } = availability;

  const buttonText = getButtonText(actionState);
  const classNames = getButtonClass(actionState);

  const disabled = actionDisabled(actionState);

  const isLoading = isReserving || isCancelling || isLeavingWaitlist;

  const handlePress = (actionState: ClassActionState) => {
    switch (actionState) {
      case ClassActionState.CAN_RESERVE_BOTH:
      case ClassActionState.CAN_RESERVE_LIVE:
        return reserveClass({
          class_id: data.id,
          date: formatDate(selectedDate),
          is_virtual: false,
          type: 'class',
        });
      case ClassActionState.CAN_RESERVE_VIRTUAL:
        return reserveClass({
          class_id: data.id,
          date: formatDate(selectedDate),
          is_virtual: true,
          type: 'class',
        });
      case ClassActionState.CAN_JOIN_WAITLIST: {
        const payload: {
          class_id: number;
          date: string;
          type: 'class';
          is_virtual?: boolean;
        } = {
          class_id: data.id,
          date: formatDate(selectedDate),
          type: 'class',
        };
        if (data.class_type === ClassType.VIRTUAL) {
          payload.is_virtual = true;
        } else if (data.class_type === ClassType.LIVE) {
          payload.is_virtual = false;
        }
        const classPayload: ReservationData = payload;
        return reserveClass(classPayload);
      }
      case ClassActionState.USER_ON_WAITLIST: {
        const cuw = data?.current_user_waitlist as
          | (Record<string, string | number> & {
              id?: number;
              waitlist_id?: number;
              waitlistId?: number;
            })
          | undefined;
        const waitlistId = Number(
          cuw?.waitlist_id ?? cuw?.id ?? cuw?.waitlistId
        );
        if (Number.isFinite(waitlistId) && waitlistId > 0) {
          return exitWaitlist(waitlistId);
        }
        return noop;
      }
      case ClassActionState.USER_HAS_RESERVATION: {
        const rid = data?.current_user_reservation?.id;
        if (typeof rid === 'number' || typeof rid === 'string') {
          return cancelReservation(Number(rid));
        }
        return noop;
      }
      default:
        return noop;
    }
  };

  return (
    <>
      <Button
        size={size}
        variant={'outline'}
        className={`rounded-full ${classNames}`}
        disabled={disabled}
        onPress={e => {
          e.stopPropagation();
          if (actionState === ClassActionState.CAN_RESERVE_BOTH) {
            setShowTypeSheet(true);
          } else {
            handlePress(actionState);
          }
        }}
      >
        <ButtonText className={`${classNames}`}>{buttonText}</ButtonText>
        {actionState === ClassActionState.CAN_RESERVE_BOTH && (
          <Icon as={ChevronDown} size="sm" className={`ml-2 ${classNames}`} />
        )}
        {isLoading && <ButtonSpinner color="gray" className="ml-2" />}
      </Button>

      <Actionsheet
        isOpen={showTypeSheet}
        onClose={() => setShowTypeSheet(false)}
      >
        <ActionsheetBackdrop className="bg-[#000] opacity-50" />
        <ActionsheetContent>
          <ActionsheetDragIndicatorWrapper>
            <ActionsheetDragIndicator />
          </ActionsheetDragIndicatorWrapper>

          <Text className="text-xl font-dm-sans-bold self-start mb-2 px-2">
            Reservation option
          </Text>

          <ActionsheetItem
            onPress={() => {
              setShowTypeSheet(false);
              reserveClass({
                class_id: data.id,
                date: formatDate(selectedDate),
                is_virtual: true,
                type: 'class',
              });
            }}
          >
            <ActionsheetItemText className="text-base">
              Reserve virtually
            </ActionsheetItemText>
          </ActionsheetItem>

          <ActionsheetItem
            onPress={() => {
              setShowTypeSheet(false);
              reserveClass({
                class_id: data.id,
                date: formatDate(selectedDate),
                is_virtual: false,
                type: 'class',
              });
            }}
          >
            <ActionsheetItemText className="text-base">
              Reserve live class
            </ActionsheetItemText>
          </ActionsheetItem>
        </ActionsheetContent>
      </Actionsheet>

      {showToast && (
        <StatusActionSheet
          isOpen={showToast}
          onClose={() => setShowToast(false)}
          status={error ? 'error' : 'success'}
          title={error ? 'Error occurred' : 'Reservation made'}
          description={error?.message ?? ''}
          actions={
            error
              ? []
              : reserveActions(data, selectedDate, {
                  isCalendarSynced,
                  onCalendarSynced: () => {
                    setCalendarSynced(true);
                    setShowCalendarSheet(true);
                  },
                })
          }
        />
      )}

      <CalendarSuccessActionSheet
        isOpen={showCalendarSheet}
        onClose={() => setShowCalendarSheet(false)}
      />
    </>
  );
};
