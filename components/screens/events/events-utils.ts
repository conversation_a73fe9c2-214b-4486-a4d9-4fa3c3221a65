import { ActivityCardData } from "@/components/shared/activity-card";
import { EventResponse } from "@/data/screens/events/types";

export const convertEventToActivityCard = (
    event: EventResponse
  ): ActivityCardData => ({
    id: String(event.id),
    title: event.name,
    imageUrl: event.image_url || undefined,
    startTime: event.start_time,
    endTime: event.end_time,
    spotsLeft: Math.max(0, event.spots - event.reservation_count),
    instructor: '', // Events don't have instructors in this API
    location: `${event.room_name}, ${event.gym_name}`,
    isFavorite: event.is_favourite,
    reservationId: event.reservations[0]?.id || undefined,
    date: event.date,
  });
  
  export const getStatusButtonConfig = (event: EventResponse) => {
    const spotsLeft = Math.max(0, event.spots - event.reservation_count);
    const hasReservation = event.reservations && event.reservations.length > 0;
  
    // If user has a reservation, show cancel option
    if (hasReservation) {
      return {
        variant: 'cancel_reservation' as const,
        text: 'Cancel reservation',
        disabled: false,
      };
    }
  
    // If event is not active, show cancelled
    if (!event.active) {
      return {
        variant: 'event_cancelled' as const,
        text: 'Event cancelled',
        disabled: true,
      };
    }
  
    // If no spots left, check if waitlist is allowed
    if (spotsLeft === 0) {
      if (event.allow_waitlist) {
        return {
          variant: 'join_waitlist' as const,
          text: 'Join waitlist',
          disabled: false,
        };
      } else {
        return {
          variant: 'event_full' as const,
          text: 'Event full',
          disabled: true,
        };
      }
    }
  
    // Default case - allow reservation
    return { variant: 'reserve' as const, text: 'Reserve', disabled: false };
  };