import React, { useState } from 'react';
import {
  SafeAreaView,
  useSafeAreaInsets,
} from 'react-native-safe-area-context';
import { VStack } from '@/components/ui/vstack';
import { HStack } from '@/components/ui/hstack';
import { Text } from '@/components/ui/text';
import { Pressable } from '@/components/ui/pressable';
import { Icon } from '@/components/ui/icon';
import { ScrollView, Alert, Platform } from 'react-native';
import { router } from 'expo-router';
import {
  User,
  Bell,
  Lock,
  Moon,
  Eye,
  Users,
  Trash2,
  FileText,
  LogOut,
  ArrowLeft,
  Edit,
} from 'lucide-react-native';
import { useSession } from '@/modules/login/auth-provider';
import { Switch } from '@/components/ui/switch';
import { Button, ButtonText } from '@/components/ui/button';
import {
  ProfileAvatar,
  AccountMenuItem,
  AccountMenuSection,
  LogoutActionSheet,
} from '@/components/screens/profile';
import { useUserProfile } from '@/hooks/useUserProfile';

const Account = () => {
  const insets = useSafeAreaInsets();
  const { data: session, signOut } = useSession();
  const { getInitials, getFullName, getUsername, profileData } =
    useUserProfile();

  const [showLogoutModal, setShowLogoutModal] = useState(false);

  // Calculate bottom padding for tab bar
  const tabBarHeight = 78 + 16 + (Platform.OS === 'ios' ? insets.bottom : 16);
  const contentBottomPadding = tabBarHeight + 16;

  const handleLogout = () => {
    setShowLogoutModal(true);
  };

  const handleConfirmLogout = () => {
    setShowLogoutModal(false);
    signOut();
  };

  const handleCancelLogout = () => {
    setShowLogoutModal(false);
  };

  return (
    <SafeAreaView className="flex-1 bg-background-50">
      <VStack className="flex-1">
        {/* Header */}
        <HStack className="px-4 py-4 bg-white items-center">
          <Pressable onPress={() => router.back()} className="mr-4">
            <Icon as={ArrowLeft} size="lg" className="text-typography-700" />
          </Pressable>
          <Text className="text-2xl font-dm-sans-bold text-typography-900">
            Account
          </Text>
        </HStack>

        <ScrollView
          className="flex-1"
          showsVerticalScrollIndicator={false}
          contentContainerStyle={{
            paddingTop: 16,
            paddingBottom: contentBottomPadding,
          }}
        >
          {/* User Profile Header */}
          <HStack className="px-4 py-6 bg-white mx-4 rounded-2xl mb-6 shadow-sm items-center">
            <ProfileAvatar
              imageUri={profileData.profileImage}
              initials={getInitials()}
              size="xl"
              showEditButton={false}
            />
            <VStack className="flex-1 ml-4">
              <Text className="text-lg font-dm-sans-bold text-typography-900">
                {getFullName()}
              </Text>
              <Text className="text-sm font-dm-sans-regular text-typography-500">
                @{getUsername()}
              </Text>
            </VStack>
            <Pressable onPress={() => router.push('/profile')}>
              <Icon as={Edit} size="lg" className="text-primary-500" />
            </Pressable>
          </HStack>

          {/* General Section */}
          <AccountMenuSection title="General">
            <AccountMenuItem
              icon={User}
              title="My profile"
              onPress={() => router.push('/profile')}
            />
            <AccountMenuItem
              icon={Users}
              title="My goals"
              onPress={() =>
                Alert.alert(
                  'Coming Soon',
                  'My goals feature will be available soon!'
                )
              }
              isLast={true}
            />
          </AccountMenuSection>

          {/* Settings and Security Section */}
          <AccountMenuSection title="Settings and Security">
            <AccountMenuItem
              icon={Bell}
              title="Notification preference"
              onPress={() =>
                Alert.alert(
                  'Coming Soon',
                  'Notification preferences will be available soon!'
                )
              }
            />
            <AccountMenuItem
              icon={Lock}
              title="Change password"
              onPress={() =>
                Alert.alert(
                  'Coming Soon',
                  'Change password feature will be available soon!'
                )
              }
            />
            <AccountMenuItem
              icon={Moon}
              title="Dark theme"
              showChevron={false}
              rightElement={
                <Switch
                  value={false}
                  onValueChange={() =>
                    Alert.alert(
                      'Coming Soon',
                      'Dark theme will be available soon!'
                    )
                  }
                  trackColor={{ true: '#00BFE0', false: '#D1D5DB' }}
                  size="md"
                />
              }
            />
            <AccountMenuItem
              icon={Eye}
              title="Make profile public"
              showChevron={false}
              rightElement={
                <Switch
                  value={false}
                  onValueChange={() =>
                    Alert.alert(
                      'Coming Soon',
                      'Profile visibility settings will be available soon!'
                    )
                  }
                  trackColor={{ true: '#00BFE0', false: '#D1D5DB' }}
                  size="md"
                />
              }
            />
            <AccountMenuItem
              icon={Users}
              title="Make avatar public"
              showChevron={false}
              rightElement={
                <Switch
                  value={false}
                  onValueChange={() =>
                    Alert.alert(
                      'Coming Soon',
                      'Avatar visibility settings will be available soon!'
                    )
                  }
                  trackColor={{ true: '#00BFE0', false: '#D1D5DB' }}
                  size="md"
                />
              }
            />
            <AccountMenuItem
              icon={Trash2}
              title="Delete account"
              onPress={() =>
                Alert.alert(
                  'Delete Account',
                  'This feature will be available soon!'
                )
              }
              titleColor="text-error-500"
              iconColor="text-error-500"
              isLast={true}
            />
          </AccountMenuSection>

          {/* Legal Section */}
          <AccountMenuSection title="Legal">
            <AccountMenuItem
              icon={FileText}
              title="Terms of service"
              onPress={() =>
                Alert.alert(
                  'Coming Soon',
                  'Terms of service will be available soon!'
                )
              }
              isLast={true}
            />
          </AccountMenuSection>

          {/* Logout Button */}
          <VStack className="px-4 mt-6">
            <Button
              onPress={handleLogout}
              variant="outline"
              className="w-full rounded-full border-error-500"
            >
              <Icon as={LogOut} size="md" className="text-error-500 mr-2" />
              <ButtonText className="text-error-500 font-dm-sans-medium">
                Logout
              </ButtonText>
            </Button>
          </VStack>
        </ScrollView>
      </VStack>

      {/* Logout Modal */}
      <LogoutModal
        isOpen={showLogoutModal}
        onClose={handleCancelLogout}
        onConfirm={handleConfirmLogout}
      />
    </SafeAreaView>
  );
};

export default Account;
