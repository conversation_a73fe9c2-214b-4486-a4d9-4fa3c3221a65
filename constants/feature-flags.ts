import Constants from 'expo-constants';

const extra = (Constants?.expoConfig?.extra || {}) as Record<string, any>;

const readRaw = (keys: string[]): string | undefined => {
  for (const k of keys) {
    const v = process.env[k] ?? extra[k];
    if (v !== undefined && v !== null) return String(v);
  }
  return undefined;
};

const toBool = (val?: string): boolean => {
  if (val === undefined) return false;
  const s = String(val).toLowerCase();
  return s === '1' || s === 'true' || s === 'yes' || s === 'on';
};

const enableNewHomeRaw = readRaw([
  'EXPO_PUBLIC_ENABLE_NEW_HOME',
  'EXPO_PUBLIC_ENABLE_NEW_HOME_SCREEN',
]);

const useHomeMocksRaw = readRaw(['EXPO_PUBLIC_USE_HOME_MOCKS']);

export const featureFlags = {
  ENABLE_NEW_HOME: toBool(enableNewHomeRaw),
  // default ON if not provided
  USE_HOME_MOCKS:
    useHomeMocksRaw === undefined ? true : toBool(useHomeMocksRaw),
} as const;
